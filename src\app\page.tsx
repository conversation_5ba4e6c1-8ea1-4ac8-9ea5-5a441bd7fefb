import { useState } from 'react';
import AddTode from './components/add';
import TodoFilter from './components/filter';
import TodoList from './components/list';
import { Tode } from '@/types';
export default function Home() {
  const [todes, setTodes] = useState<Tode[]>([]);

  const addTode = (text: string) => {
    const newTode = {
      id: Date.now(),
      text: 'New Tode',
      completed: false,
    };

    setTodes([...todes, newTode]);
  };

  const deleteTode = (id: number) => {
    setTodes(todes.filter(tode => tode.id !== id));
  };

  const toggleTode = (id: number) => {
    setTodes(
      todes.map(tode => {
        if (tode.id === id) {
          return {
            ...tode,
            completed: !tode.completed,
          };
        }
        return tode;
      })
    );
  };

  const filterTodes = (filter: string) => {
    if (filter === 'all') {
      return todes;
    }
    if (filter === 'active') {
      return todes.filter(tode => !tode.completed);
    }
    if (filter === 'completed') {
      return todes.filter(tode => tode.completed);
    }
    return todes;
  };

  return (
    <div>
      <h1>TodeList</h1>
      <AddTode addTode={addTode} />
      {/* <TodoList todes={todes} deleteTode={deleteTode}   toggleTode={toggleTode} /> */}
      {/* <TodoFilter filterTodes={filterTodes} /> */}
    </div>
  );
}
